# iChat AI Assistant

An AI-powered internal chat agent with document processing, vector search, and escalation workflow capabilities designed to reduce managerial time spent on routine Q&A.

## Executive Summary

The company is seeking to reduce managerial time spent on routine subordinate Q&A by implementing an AI-powered internal chat agent. The system will act as the first line of support for employee questions, leveraging existing process documentation and ongoing learning. When the AI cannot respond confidently, queries will escalate to human managers for validation and knowledge base improvement.

We are considering OpenArc either as a development partner for this initiative or as a recruiting partner to source a full-time contract engineer to lead the build.

## Business Case

### Problem Statement
Managers at the company frequently handle repetitive operational questions that divert attention from high-value work. These questions are:
- **Time-consuming** — Managers repeat the same answers across different teams
- **Inconsistent** — Employees may receive different answers depending on whom they ask
- **Fragmented** — Knowledge is trapped in chats or emails and not systematically captured

This creates inefficiency, slows employee response time, and prevents scalable knowledge sharing.

### Strategic Value & Benefits
- **Reduce managerial workload** by automating routine Q&A
- **Improve employee productivity** with faster, more consistent answers
- **Capture institutional knowledge** that compounds in value over time
- **Enhance onboarding and compliance** by providing always-available guidance

### Success Metrics
- Reduce manager time spent on routine Q&A
- Achieve high response accuracy without escalation
- Improve employee satisfaction with information access
- Build comprehensive, searchable knowledge base

## Solution Overview

### Core Capabilities
An internal AI chat agent that:
- **Centralizes knowledge** — Trains on internal process documents, SOPs, and historic chat conversations
- **Learns continuously** — Escalates to managers when uncertain, captures validated responses, and incorporates them back into its knowledge
- **Standardizes answers** — Provides consistent, policy-aligned responses
- **Scales onboarding & training** — Serves as a "knowledge companion" for new employees

### Key Features
- Knowledge ingestion from documents and chat archives
- Escalation workflow for unanswered queries
- Versioning and audit trail for responses
- Guardrails to prevent speculative or incorrect answers

## Technical Requirements

### System Architecture
- **LLM Platform**: Configurable Large Language Model (LLM) with retrieval-augmented generation (RAG)
- **LLM Providers**: Support for OpenAI, Anthropic, or other LLM providers
- **Vector Database**: OpenAI embeddings with pgvector for vector database in Supabase
- **Hosting**: Cloud-hosted solution
- **Language Support**: English only

### Integration Requirements
- **Document Formats**: Ability to ingest PDFs, Word docs, and other formats
- **Chat Integration**: Integration with Trillian and internal process documentation repositories
- **Authentication**: OAuth2 using company credentials (web/Trillian), API key authentication

### Performance & Scale
- **Concurrent Users**: 5-10 expected concurrent users
- **Conversation Support**: Multi-turn conversations
- **Data Retention**: All queries and chats retained for 30-day period
- **Initial Content**: Approximately 200 documents for initial upload

### Security & Compliance
- **Authentication**: OAuth2 with company credentials
- **Data Retention**: 30-day retention policy for queries and chats
- **Audit Requirements**: Compliance alignment with safety industry requirements

## Functional Specifications

### User Interface & Access
- **Trillian Bot**: Primary access via Trillian integration
- **Command Line Tool**: Full-featured CLI for document management, user management, and chat functionality
- **Administrative Interface**: Web interface for managing escalated queries

### Knowledge Management
- **Document Ingestion**: Support for PDFs, Word docs, and other formats
- **Knowledge Base**: Approximately 200 initial documents
- **Versioning**: Audit trail for responses and knowledge updates

### Escalation Workflow
- **Automatic Escalation**: Triggered when LLM confidence is below threshold
- **Manual Escalation**: Users can escalate queries directly
- **Query Management**: All managers see escalated queries
- **Response Refinement**: Anyone can provide feedback and refine responses
- **Query Status**: Escalated queries can be closed/reopened as needed

### Feedback & Learning Loop
- **Response Feedback**: Users and administrators can provide feedback on AI responses
- **Continuous Learning**: Validated responses incorporated back into knowledge base
- **Quality Measurement**: Feedback system to measure and improve response quality

### Command Line Interface
- **Chat Functionality**: Full conversational AI access equivalent to web interface
- **Document Management**: Upload, update, delete, and organize knowledge base documents
- **User Management**: Create, modify, and manage user accounts and permissions
- **Query Management**: View, respond to, and manage escalated queries
- **System Administration**: Configure system settings, monitor usage, and export data
- **Batch Operations**: Support for bulk document uploads and user management tasks

## Implementation Plan

### Development Approach
- **MVP Strategy**: Build as MVP first, then enhance based on feedback and usage
- **Human-in-the-loop**: Escalation and reinforcement learning system
- **Iterative Enhancement**: Continuous improvement based on user feedback

### Milestones

#### MVP Release (7-8 weeks)
**Core Features Delivered:**
- ✅ Basic backend API with JWT authentication
- ✅ Trillian AI chat system with OpenAI integration and RAG capabilities
- ✅ PostgreSQL database with pgvector for vector embeddings
- ✅ Document upload, text extraction, chunking, and vector storage
- ✅ Semantic search and retrieval-augmented generation
- ✅ Admin web interface for document management
- ✅ Basic user management and role-based access
- ✅ Supabase cloud deployment with managed services
- ✅ Prompt engineering and response formatting

**MVP Success Criteria:**
- AI chat agent responds to employee questions using uploaded documents
- Administrators can upload and manage documents through web interface
- System runs in the cloud with proper authentication
- Basic vector search provides relevant document retrieval

#### Post-MVP Release (Future Enhancement)
**Advanced Features for Subsequent Release:**
- 🔄 Escalation and feedback system with human-in-the-loop learning
- 🔄 Advanced analytics and monitoring dashboards
- 🔄 Supabase cloud deployment with production scaling
- 🔄 RAG pipeline optimization and evaluation framework
- 🔄 Document versioning and audit trail system
- 🔄 Advanced security features and compliance controls
- 🔄 Multi-turn conversation context management
- 🔄 Automated confidence scoring and escalation triggers

**Post-MVP Goals:**
- Reduce manager time spent on routine Q&A by 60%
- Achieve 85%+ response accuracy without escalation
- Support 50+ concurrent users across multiple teams
- Comprehensive audit trail for compliance requirements

### Governance Framework
- **Escalation Protocol**: Clear protocol when AI confidence is low
- **Feedback Loop**: Continuous manager and user feedback integration
- **Compliance**: Audit and compliance alignment with safety industry requirements

## 🏗️ Architecture

This is a monorepo containing:

- **Backend**: ASP.NET Core 10+ + C# API server
- **Frontend**: React + TypeScript + Vite web application with Supabase integration
- **Supabase**: Managed database, auth, storage, and real-time services

## 📋 Prerequisites

- .NET 10+ SDK
- Node.js 18+ and npm 9+ (for frontend only)
- Supabase CLI (for local development)
- Redis (for caching and sessions)

## 🛠️ Development Setup

### Option 1: Local Development

1. **Clone and install dependencies**:

   ```bash
   git clone <repository-url>
   cd ichat
   dotnet restore
   cd frontend && npm install && cd ..
   ```

2. **Set up environment**:

   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Set up Supabase**:

   ```bash
   docker-compose -f docker-compose.dev.yml up -d db redis
   ```

4. **Set up database**:

   ```bash
   cd backend
   dotnet ef database update
   cd ..
   ```

5. **Start development servers**:

   ```bash
   # Terminal 1 - Backend
   cd backend && dotnet run

   # Terminal 2 - Frontend
   cd frontend && npm run dev
   ```

### Option 2: DevContainer (Recommended)

1. Open the project in VS Code
2. Install the "Dev Containers" extension
3. Press `Ctrl+Shift+P` and select "Dev Containers: Reopen in Container"
4. The development environment will be automatically set up

## 📦 Available Scripts

### Root Level

- `dotnet run` - Start backend development server (from backend/ directory)
- `npm run dev` - Start frontend development server (from frontend/ directory)
- `dotnet build` - Build backend solution
- `dotnet test` - Run all backend tests
- `dotnet format` - Format C# code
- `docker-compose -f docker-compose.dev.yml up` - Start development environment with Docker

### Backend-Specific (from backend/ directory)

- `dotnet run` - Start backend development server
- `dotnet build` - Build backend
- `dotnet test` - Run backend tests
- `dotnet ef migrations add <name>` - Create new migration
- `dotnet ef database update` - Apply migrations

### Frontend-Specific (from frontend/ directory)

- `npm run dev` - Start frontend development server
- `npm run build` - Build frontend for production
- `npm run test` - Run frontend tests
- `npm run lint` - Lint frontend code

## 🌐 Services

When running in development mode:

- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:5000 (HTTPS: https://localhost:5001)
- **API Documentation**: http://localhost:5000/swagger
- **Database**: localhost:5432
- **Redis**: localhost:6379

## 🐳 Docker Usage

### Development

```bash
# Start all services
docker-compose -f docker-compose.dev.yml up

# Start specific service
docker-compose -f docker-compose.dev.yml up backend

# View logs
docker-compose -f docker-compose.dev.yml logs -f backend
```

### Production

```bash
# Build and start
docker-compose up -d

# Scale services
docker-compose up -d --scale backend=2

# View status
docker-compose ps
```

### CLI Tool

```bash
# Build CLI container
docker-compose build cli

# Run CLI commands
docker-compose run --rm cli auth login
docker-compose run --rm cli docs upload document.pdf
docker-compose run --rm cli chat start
```

## 🗄️ Database

The project uses Supabase (managed PostgreSQL with pgvector) for vector similarity search, authentication, storage, and real-time features, with Entity Framework Core for complex backend operations.

### Migrations

```bash
# Navigate to backend directory
cd backend

# Generate migration
dotnet ef migrations add MigrationName

# Apply migrations to development database
dotnet ef database update

# Apply migrations to production
dotnet ef database update --connection "your-production-connection-string"

# View migration history
dotnet ef migrations list
```

### Schema

Key entities:

- Users (authentication and roles)
- Documents (uploaded files and content)
- DocumentChunks (text chunks with embeddings)
- ChatSessions (conversation threads)
- Messages (chat messages with confidence scores)
- EscalatedQueries (human escalation workflow)
- Feedback (user feedback on responses)

## 🔧 Configuration

### Environment Variables

Copy `.env.example` to `.env` and configure:

- **Database**: PostgreSQL connection details
- **Redis**: Redis connection for caching
- **LLM APIs**: OpenAI and Anthropic API keys
- **Authentication**: JWT secrets and OAuth configuration
- **File Upload**: Size limits and allowed types
- **Monitoring**: Logging levels and Application Insights

### LLM Configuration

The system supports multiple LLM providers:

- OpenAI (GPT-3.5, GPT-4)
- Anthropic (Claude 3 models)

Configure in environment variables or through the admin interface.

## 🧪 Testing

```bash
# Run backend tests
cd backend && dotnet test

# Run backend tests with coverage
cd backend && dotnet test --collect:"XPlat Code Coverage"

# Run tests in watch mode
cd backend && dotnet watch test

# Run frontend tests
cd frontend && npm test

# Run frontend tests with coverage
cd frontend && npm run test:coverage
```

## 📝 API Documentation

The backend API is documented with OpenAPI/Swagger. Access the interactive documentation at:

- Development: http://localhost:5000/swagger
- Production: https://your-domain.com/swagger

## 🚀 Deployment

### Supabase Cloud (Recommended)

1. **Set up Supabase project**:
   - Create new project at supabase.com
   - Configure authentication providers
   - Static Web Apps for React frontend
   - Database for PostgreSQL
   - Container Registry for CLI
   - Key Vault for secrets

2. **Deploy application**:
   - Deploy React frontend to Supabase hosting or CDN
   - Deploy .NET backend to your preferred hosting
   - Configure Supabase Edge Functions for API logic
   - Environment-specific configurations

3. **Deploy**:

   ```bash
   # Build backend
   cd backend && dotnet publish -c Release -o ./publish

   # Build frontend
   cd frontend && npm run build

   # Deploy to Supabase
   supabase functions deploy --project-ref YOUR_PROJECT_REF
   ```

### Docker Deployment

```bash
# Production deployment
docker-compose up -d

# With custom configuration
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

## 🔒 Security

- JWT-based authentication with ASP.NET Core Identity
- Role-based access control
- Input validation with FluentValidation
- Rate limiting with ASP.NET Core middleware
- Security headers built into ASP.NET Core
- CORS configuration
- Environment-based secrets with Supabase configuration

## 📊 Monitoring

- Serilog structured logging
- Health check endpoints
- Application metrics with Application Insights
- Error tracking
- Performance monitoring

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Run linting and tests
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:

- Check the documentation in `/docs`
- Review the project plan in `PROJECT_PLAN.md`
- Check technical specifications in `TECHNICAL_STACK.md`
- Open an issue on GitHub
