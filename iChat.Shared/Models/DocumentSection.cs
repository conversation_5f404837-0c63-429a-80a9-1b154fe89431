using System.ComponentModel.DataAnnotations;

namespace iChat.Shared.Models;

public class DocumentSection
{
    public Guid Id { get; set; }
    public Guid DocumentId { get; set; }
    public string Content { get; set; } = string.Empty;
    public int SectionIndex { get; set; }
    public int TokenCount { get; set; }
    public float[] Embedding { get; set; } = Array.Empty<float>();
    public DateTime CreatedAt { get; set; }
    
    public virtual Document Document { get; set; } = null!;
}
