using System.ComponentModel.DataAnnotations;
using iChat.Shared.Enums;

namespace iChat.Shared.Models;

public class EscalatedQuery
{
    public Guid Id { get; set; }
    public Guid MessageId { get; set; }
    public QueryStatus Status { get; set; }
    public Guid? AssignedTo { get; set; }
    public string? Resolution { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? ResolvedAt { get; set; }
    
    public virtual Message Message { get; set; } = null!;
    public virtual User? AssignedToUser { get; set; }
}
