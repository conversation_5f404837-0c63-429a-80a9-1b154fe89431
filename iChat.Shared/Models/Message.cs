using System.ComponentModel.DataAnnotations;
using iChat.Shared.Enums;

namespace iChat.Shared.Models;

public class Message
{
    public Guid Id { get; set; }
    public Guid SessionId { get; set; }
    public string Content { get; set; } = string.Empty;
    public MessageRole Role { get; set; }
    public double? Confidence { get; set; }
    public bool Escalated { get; set; }
    public DateTime CreatedAt { get; set; }
    
    public virtual ChatSession Session { get; set; } = null!;
    public virtual EscalatedQuery? EscalatedQuery { get; set; }
}
