{"format": 1, "restore": {"/Users/<USER>/Source/ichat/iChat.Shared/iChat.Shared.csproj": {}}, "projects": {"/Users/<USER>/Source/ichat/iChat.Shared/iChat.Shared.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Source/ichat/iChat.Shared/iChat.Shared.csproj", "projectName": "iChat.Shared", "projectPath": "/Users/<USER>/Source/ichat/iChat.Shared/iChat.Shared.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Source/ichat/iChat.Shared/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"/usr/local/share/dotnet/library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"System.Text.Json": {"target": "Package", "version": "[9.0.9, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.305/PortableRuntimeIdentifierGraph.json"}}}}}