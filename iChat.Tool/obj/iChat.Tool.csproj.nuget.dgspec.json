{"format": 1, "restore": {"/Users/<USER>/Source/ichat/iChat.Tool/iChat.Tool.csproj": {}}, "projects": {"/Users/<USER>/Source/ichat/iChat.Shared/iChat.Shared.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Source/ichat/iChat.Shared/iChat.Shared.csproj", "projectName": "iChat.Shared", "projectPath": "/Users/<USER>/Source/ichat/iChat.Shared/iChat.Shared.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Source/ichat/iChat.Shared/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"/usr/local/share/dotnet/library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"System.Text.Json": {"target": "Package", "version": "[9.0.9, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.305/PortableRuntimeIdentifierGraph.json"}}}, "/Users/<USER>/Source/ichat/iChat.Tool/iChat.Tool.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Source/ichat/iChat.Tool/iChat.Tool.csproj", "projectName": "iChat.Tool", "projectPath": "/Users/<USER>/Source/ichat/iChat.Tool/iChat.Tool.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Source/ichat/iChat.Tool/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"/usr/local/share/dotnet/library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"/Users/<USER>/Source/ichat/iChat.Shared/iChat.Shared.csproj": {"projectPath": "/Users/<USER>/Source/ichat/iChat.Shared/iChat.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.9, )"}, "Microsoft.EntityFrameworkCore.Design": {"target": "Package", "version": "[9.0.9, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[9.0.9, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[9.0.9, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[9.0.4, )"}, "Serilog.Extensions.Hosting": {"target": "Package", "version": "[9.0.0, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[6.0.0, )"}, "Supabase": {"target": "Package", "version": "[1.1.1, )"}, "System.CommandLine": {"target": "Package", "version": "[*******, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.305/PortableRuntimeIdentifierGraph.json"}}}}}