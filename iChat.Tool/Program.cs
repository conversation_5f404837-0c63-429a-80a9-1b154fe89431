using Spectre.Console.Cli;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Configuration;
using Serilog;
using iChat.Tool.Commands;
using iChat.Tool.Services;

namespace iChat.Tool;

class Program
{
    static int Main(string[] args)
    {
        // Configure Serilog
        Log.Logger = new LoggerConfiguration()
            .WriteTo.Console()
            .CreateLogger();

        try
        {
            var host = CreateHostBuilder(args).Build();

            var app = new CommandApp();
            app.Configure(config =>
            {
                config.SetApplicationName("iChat.Tool");
                config.SetApplicationVersion("1.0.0");

                // Add commands
                config.AddCommand<VersionCommand>("version")
                    .WithDescription("Display the current version of iChat.Tool");

                config.AddCommand<InfoCommand>("info")
                    .WithDescription("Display information about how to use iChat.Tool");

                config.AddDelegate<MigrateSettings>("migrate", (context, settings) =>
                {
                    var migrateCommand = new MigrateCommand(host.Services);
                    return migrateCommand.ExecuteAsync(context, settings).GetAwaiter().GetResult();
                })
                .WithDescription("Run database migrations");
            });

            return app.Run(args);
        }
        catch (Exception ex)
        {
            Log.Fatal(ex, "Application terminated unexpectedly");
            return 1;
        }
        finally
        {
            Log.CloseAndFlush();
        }
    }

    static IHostBuilder CreateHostBuilder(string[] args) =>
        Host.CreateDefaultBuilder(args)
            .UseSerilog()
            .ConfigureServices((context, services) =>
            {
                services.AddTransient<IMigrationService, MigrationService>();
            });
}
