using System.CommandLine;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Configuration;
using Serilog;
using iChat.Tool.Commands;
using iChat.Tool.Services;

namespace iChat.Tool;

class Program
{
    static async Task<int> Main(string[] args)
    {
        // Configure Serilog
        Log.Logger = new LoggerConfiguration()
            .WriteTo.Console()
            .CreateLogger();

        try
        {
            var host = CreateHostBuilder(args).Build();

            var rootCommand = new RootCommand("iChat.Tool - Command line interface for iChat administration");

            // Add commands
            rootCommand.AddCommand(new VersionCommand());
            rootCommand.AddCommand(new InfoCommand());
            rootCommand.AddCommand(new MigrateCommand(host.Services));

            return await rootCommand.InvokeAsync(args);
        }
        catch (Exception ex)
        {
            Log.Fatal(ex, "Application terminated unexpectedly");
            return 1;
        }
        finally
        {
            Log.CloseAndFlush();
        }
    }

    static IHostBuilder CreateHostBuilder(string[] args) =>
        Host.CreateDefaultBuilder(args)
            .UseSerilog()
            .ConfigureServices((context, services) =>
            {
                services.AddTransient<IMigrationService, MigrationService>();
            });
}
