using System.CommandLine;
using System.Reflection;

namespace iChat.Tool.Commands;

public class VersionCommand : Command
{
    public VersionCommand() : base("version", "Display the current version of iChat.Tool")
    {
        this.SetHandler(() =>
        {
            var version = Assembly.GetExecutingAssembly().GetName().Version;
            var assemblyVersion = version?.ToString() ?? "Unknown";
            
            var informationalVersion = Assembly.GetExecutingAssembly()
                .GetCustomAttribute<AssemblyInformationalVersionAttribute>()?.InformationalVersion ?? assemblyVersion;

            Console.WriteLine($"iChat.Tool version {informationalVersion}");
            Console.WriteLine($"Assembly version: {assemblyVersion}");
            Console.WriteLine($".NET Runtime: {Environment.Version}");
            Console.WriteLine($"OS: {Environment.OSVersion}");
        });
    }
}
