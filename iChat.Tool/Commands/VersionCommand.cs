using Spectre.Console;
using Spectre.Console.Cli;
using System.ComponentModel;
using System.Reflection;

namespace iChat.Tool.Commands;

public class VersionSettings : CommandSettings
{
}

public class VersionCommand : Command<VersionSettings>
{
    public override int Execute(CommandContext context, VersionSettings settings)
    {
        var version = Assembly.GetExecutingAssembly().GetName().Version;
        var assemblyVersion = version?.ToString() ?? "Unknown";

        var informationalVersion = Assembly.GetExecutingAssembly()
            .GetCustomAttribute<AssemblyInformationalVersionAttribute>()?.InformationalVersion ?? assemblyVersion;

        // Create a nice table for version information
        var table = new Table();
        table.AddColumn("Property");
        table.AddColumn("Value");

        table.AddRow("iChat.Tool Version", $"[green]{informationalVersion}[/]");
        table.AddRow("Assembly Version", assemblyVersion);
        table.AddRow(".NET Runtime", Environment.Version.ToString());
        table.AddRow("Operating System", Environment.OSVersion.ToString());

        AnsiConsole.Write(table);

        return 0;
    }
}
