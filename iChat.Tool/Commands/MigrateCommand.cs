using System.CommandLine;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using iChat.Tool.Services;

namespace iChat.Tool.Commands;

public class MigrateCommand : Command
{
    private readonly IServiceProvider _serviceProvider;

    public MigrateCommand(IServiceProvider serviceProvider) : base("migrate", "Run database migrations")
    {
        _serviceProvider = serviceProvider;

        var checkOption = new Option<bool>("--check", "Check database connection without running migrations");
        AddOption(checkOption);

        this.SetHandler(async (checkOnly) =>
        {
            using var scope = _serviceProvider.CreateScope();
            var migrationService = scope.ServiceProvider.GetRequiredService<IMigrationService>();
            var logger = scope.ServiceProvider.GetRequiredService<ILogger<MigrateCommand>>();

            try
            {
                if (checkOnly)
                {
                    logger.LogInformation("Checking database connection...");
                    var connected = await migrationService.CheckConnectionAsync();
                    if (connected)
                    {
                        Console.WriteLine("✅ Database connection successful");
                    }
                    else
                    {
                        Console.WriteLine("❌ Database connection failed");
                        Environment.Exit(1);
                    }
                }
                else
                {
                    Console.WriteLine("Starting database migrations...");
                    await migrationService.RunMigrationsAsync();
                    Console.WriteLine("✅ Migrations completed successfully");
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Migration command failed");
                Console.WriteLine($"❌ Migration failed: {ex.Message}");
                Environment.Exit(1);
            }
        }, checkOption);
    }
}
