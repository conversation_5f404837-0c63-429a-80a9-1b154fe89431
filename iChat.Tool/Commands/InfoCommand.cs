using System.CommandLine;

namespace iChat.Tool.Commands;

public class InfoCommand : Command
{
    public InfoCommand() : base("info", "Display information about how to use iChat.Tool")
    {
        this.SetHandler(() =>
        {
            Console.WriteLine("iChat.Tool - Command Line Interface");
            Console.WriteLine("=====================================");
            Console.WriteLine();
            Console.WriteLine("DESCRIPTION:");
            Console.WriteLine("  iChat.Tool is a command-line utility for administering and managing the iChat system.");
            Console.WriteLine("  It provides tools for database migrations, system maintenance, and configuration.");
            Console.WriteLine();
            Console.WriteLine("USAGE:");
            Console.WriteLine("  iChat.Tool [command] [options]");
            Console.WriteLine();
            Console.WriteLine("COMMANDS:");
            Console.WriteLine("  version                 Display the current version information");
            Console.WriteLine("  migrate                 Run database migrations");
            Console.WriteLine("  migrate --check         Check database connection without running migrations");
            Console.WriteLine("  info                    Display this help information");
            Console.WriteLine();
            Console.WriteLine("GLOBAL OPTIONS:");
            Console.WriteLine("  -h, --help             Show help and usage information");
            Console.WriteLine("  --version              Show version information");
            Console.WriteLine();
            Console.WriteLine("ENVIRONMENT VARIABLES:");
            Console.WriteLine("  SUPABASE_URL           Supabase project URL (required for database operations)");
            Console.WriteLine("  SUPABASE_ANON_KEY      Supabase anonymous key (required for database operations)");
            Console.WriteLine();
            Console.WriteLine("EXAMPLES:");
            Console.WriteLine("  iChat.Tool version                    # Show version information");
            Console.WriteLine("  iChat.Tool migrate                    # Run all pending migrations");
            Console.WriteLine("  iChat.Tool migrate --check            # Test database connection");
            Console.WriteLine("  iChat.Tool info                       # Show this help information");
            Console.WriteLine();
            Console.WriteLine("CONFIGURATION:");
            Console.WriteLine("  Configuration can be provided via:");
            Console.WriteLine("  - Environment variables");
            Console.WriteLine("  - appsettings.json file");
            Console.WriteLine("  - Command line options (where available)");
            Console.WriteLine();
            Console.WriteLine("For more information, visit: https://github.com/your-org/ichat");
        });
    }
}
