# Technical Stack Specifications

## Overview

This document provides detailed technical specifications for the AI-powered internal chat agent system, leveraging Supabase for managed database, authentication, real-time features, and storage, with a hybrid .NET 10+ backend architecture.

## Backend Architecture

### Project Structure

- **Shared Project**: `iChat.Shared` - POCO classes for database schema and common models
- **REST API Project**: `iChat.Api` - ASP.NET Core Web API for client-server communication
- **CLI Project**: `iChat.Tool` - Command-line interface for administration and management
- **Service Project**: `iChat.Service` - Chat bot service with LLM integration
- **Frontend Project**: `iChat.Frontend` - React web application

### Project Dependencies

```mermaid
graph TD
    A[iChat.Shared] --> B[iChat.Api]
    A --> C[iChat.Tool]
    A --> D[iChat.Service]
    E[iChat.Frontend] --> B
    D --> B
```

### Hybrid Architecture

- **Primary Backend**: .NET 10+ with ASP.NET Core for complex business logic
- **Supabase Integration**: Edge Functions for simple operations and LLM integration
- **API Design**: RESTful APIs + Supabase REST API + Supabase Realtime

### Core Framework

- **Runtime**: .NET 10+ LTS
- **Language**: C# (v12.0+)
- **Web Framework**: ASP.NET Core (v10.0+) for API and Service projects
- **Supabase SDK**: Official Supabase .NET client

### Key NuGet Packages

#### iChat.Shared

```xml
<PackageReference Include="System.ComponentModel.Annotations" Version="5.0.0" />
<PackageReference Include="System.Text.Json" Version="8.0.0" />
```

#### iChat.Api

```xml
<PackageReference Include="Microsoft.AspNetCore.App" Version="10.0.0" />
<PackageReference Include="Supabase" Version="2.0.0" />
<PackageReference Include="FluentValidation.AspNetCore" Version="11.3.0" />
<PackageReference Include="Serilog.AspNetCore" Version="10.0.0" />
<PackageReference Include="Serilog.Sinks.Console" Version="5.0.0" />
<PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
<ProjectReference Include="../iChat.Shared/iChat.Shared.csproj" />
```

#### iChat.Tool

```xml
<PackageReference Include="System.CommandLine" Version="2.0.0" />
<PackageReference Include="Microsoft.Extensions.Hosting" Version="10.0.0" />
<PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="10.0.0" />
<PackageReference Include="Supabase" Version="2.0.0" />
<PackageReference Include="Serilog.Extensions.Hosting" Version="10.0.0" />
<ProjectReference Include="../iChat.Shared/iChat.Shared.csproj" />
```

#### iChat.Service

```xml
<PackageReference Include="Microsoft.Extensions.Hosting" Version="10.0.0" />
<PackageReference Include="Microsoft.AspNetCore.SignalR.Client" Version="10.0.0" />
<PackageReference Include="Supabase" Version="2.0.0" />
<PackageReference Include="OpenAI" Version="2.0.0" />
<PackageReference Include="LangChain" Version="0.15.0" />
<PackageReference Include="Serilog.Extensions.Hosting" Version="10.0.0" />
<ProjectReference Include="../iChat.Shared/iChat.Shared.csproj" />
```

### Authentication & Security

- **Supabase Auth**: Built-in OAuth2 with company identity provider integration
- **JWT Tokens**: Supabase-managed JWT tokens for session management
- **API Keys**: Supabase API keys for backend authentication
- **Row Level Security**: Supabase RLS policies for data access control
- **Input Validation**: FluentValidation for request validation

## Database Architecture

### Primary Database

- **Platform**: Supabase PostgreSQL 15+ with pgvector
- **Vector Extension**: pgvector pre-configured for embeddings storage
- **ORM**: Entity Framework Core (v10.0+) for .NET backend + Supabase SDK for client operations
- **Hybrid Approach**: Direct SQL/PostgREST for simple operations, Entity Framework for complex queries
- **Auto-generated APIs**: PostgREST for automatic REST API generation

### Key Database Packages

#### iChat.Shared (Database Models)

```xml
<PackageReference Include="System.ComponentModel.DataAnnotations" Version="5.0.0" />
```

#### iChat.Api (Entity Framework)

```xml
<PackageReference Include="Microsoft.EntityFrameworkCore" Version="10.0.0" />
<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="10.0.0" />
<PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="10.0.0" />
<PackageReference Include="Supabase" Version="2.0.0" />
```

### Database Schema Design (iChat.Shared)

```csharp
// Core entities in iChat.Shared project
using System.ComponentModel.DataAnnotations;

public class User
{
    public Guid Id { get; set; }
    public string Email { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public UserRole Role { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }

    public virtual ICollection<ChatSession> ChatSessions { get; set; } = new List<ChatSession>();
    public virtual ICollection<Document> Documents { get; set; } = new List<Document>();
}

public class Document
{
    public Guid Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public string FilePath { get; set; } = string.Empty;
    public string MimeType { get; set; } = string.Empty;
    public Guid UploadedBy { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }

    public virtual User UploadedByUser { get; set; } = null!;
    public virtual ICollection<DocumentSection> DocumentSections { get; set; } = new List<DocumentSection>();
}

public class DocumentSection
{
    public Guid Id { get; set; }
    public Guid DocumentId { get; set; }
    public string Content { get; set; } = string.Empty;
    public int SectionIndex { get; set; }
    public int TokenCount { get; set; }
    public float[] Embedding { get; set; } = Array.Empty<float>();
    public DateTime CreatedAt { get; set; }

    public virtual Document Document { get; set; } = null!;
}

public class ChatSession
{
    public Guid Id { get; set; }
    public Guid UserId { get; set; }
    public string Title { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }

    public virtual User User { get; set; } = null!;
    public virtual ICollection<Message> Messages { get; set; } = new List<Message>();
}

public class Message
{
    public Guid Id { get; set; }
    public Guid SessionId { get; set; }
    public string Content { get; set; } = string.Empty;
    public MessageRole Role { get; set; }
    public double? Confidence { get; set; }
    public bool Escalated { get; set; }
    public DateTime CreatedAt { get; set; }

    public virtual ChatSession Session { get; set; } = null!;
    public virtual EscalatedQuery? EscalatedQuery { get; set; }
}

public class EscalatedQuery
{
    public Guid Id { get; set; }
    public Guid MessageId { get; set; }
    public QueryStatus Status { get; set; }
    public Guid? AssignedTo { get; set; }
    public string? Resolution { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? ResolvedAt { get; set; }

    public virtual Message Message { get; set; } = null!;
    public virtual User? AssignedToUser { get; set; }
}

public enum UserRole
{
    User,
    Admin,
    Manager
}

public enum MessageRole
{
    User,
    Assistant
}

public enum QueryStatus
{
    Open,
    Closed
}
```

### Vector Storage

- **Embeddings Model**: OpenAI text-embedding-ada-002
- **Vector Dimensions**: 1536 (OpenAI standard)
- **Storage Granularity**: Document sections (chunks) with embeddings
- **Similarity Search**: Cosine similarity via pgvector on document sections
- **Indexing**: HNSW index on DocumentSection.Embedding for efficient similarity search
- **Retrieval**: Section-level similarity matching with document context

## Frontend Architecture (iChat.Frontend)

### Web Application

- **Project**: `iChat.Frontend` - React web application
- **Framework**: React (v18+) with TypeScript
- **Build Tool**: Vite (v4.0+)
- **State Management**: Zustand or React Query
- **UI Framework**: Tailwind CSS + Headless UI
- **Real-time**: Supabase Realtime client for live chat
- **Authentication**: Supabase Auth client for user management
- **API Communication**: REST API calls to `iChat.Api`

### Key Frontend Dependencies

```json
{
  "react": "^18.2.0",
  "react-dom": "^18.2.0",
  "typescript": "^5.0.0",
  "vite": "^4.0.0",
  "tailwindcss": "^3.3.0",
  "@headlessui/react": "^1.7.0",
  "@supabase/supabase-js": "^2.39.0",
  "react-query": "^3.39.0",
  "react-router-dom": "^6.8.0",
  "react-hook-form": "^7.43.0"
}
```

## LLM Integration (iChat.Service)

### AI/ML Stack

- **Service Project**: `iChat.Service` - Dedicated chat bot service
- **Primary LLM**: OpenAI GPT-4 or GPT-3.5-turbo
- **Alternative LLM**: Anthropic Claude (configurable)
- **Embeddings**: OpenAI text-embedding-ada-002
- **RAG Framework**: Custom implementation using LangChain.NET
- **Communication**: Service communicates with `iChat.Api` for data access

### LLM Dependencies

```xml
<PackageReference Include="OpenAI" Version="2.0.0" />
<PackageReference Include="Anthropic.SDK" Version="0.2.0" />
<PackageReference Include="LangChain" Version="0.15.0" />
<PackageReference Include="LangChain.Providers.OpenAI" Version="0.15.0" />
<PackageReference Include="Microsoft.ML" Version="4.0.0" />
<PackageReference Include="Microsoft.ML.OnnxRuntime" Version="1.18.0" />
```

### RAG Implementation

- **Document Chunking**: Recursive character text splitter into DocumentSection entities
- **Chunk Size**: 1000 characters with 200 character overlap per section
- **Retrieval**: Top-k similarity search on DocumentSection embeddings (k=5)
- **Context Window**: 4000 tokens for GPT-3.5, 8000 for GPT-4
- **Section-based Retrieval**: Query embeddings matched against section embeddings
- **Context Assembly**: Relevant sections assembled with document metadata
- **Confidence Scoring**: Section-level confidence based on embedding similarity scores

## Document Processing

### File Processing Stack

```xml
<PackageReference Include="itext7" Version="8.0.2" />
<PackageReference Include="DocumentFormat.OpenXml" Version="3.0.0" />
<PackageReference Include="SixLabors.ImageSharp" Version="3.0.2" />
<PackageReference Include="Tesseract" Version="5.2.0" />
<PackageReference Include="MimeMapping" Version="********" />
<PackageReference Include="Supabase" Version="2.0.0" />
```

### File Storage

- **Supabase Storage**: Managed file storage with CDN
- **Bucket Organization**: Separate buckets for different document types
- **Access Control**: Row Level Security policies for file access

### Supported Formats

- **PDF**: iText7 for text extraction
- **Word Documents**: DocumentFormat.OpenXml for .docx files
- **Text Files**: Direct processing (.txt, .md)

### Processing Pipeline

1. File upload to Supabase Storage with validation
2. Text extraction based on file type
3. Content cleaning and preprocessing
4. Text chunking into DocumentSection entities
5. Vector generation for each document section
6. DocumentSection storage with embeddings in Supabase
7. Document metadata indexing with section references

## Trillian Integration

### Chat Platform Integration

```xml
<PackageReference Include="Microsoft.AspNetCore.Http" Version="10.0.0" />
<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
<PackageReference Include="Supabase" Version="2.0.0" />
```

### Integration Capabilities

- **Webhook Endpoints**: Receive messages from Trillian platform
- **Supabase Edge Functions**: Process messages and generate AI responses
- **Real-time Updates**: Supabase Realtime for live chat functionality
- **Command Support**: Handle slash commands and direct messages

### Integration Architecture

- **Message Reception**: Trillian webhooks → Supabase Edge Functions
- **AI Processing**: Edge Functions handle LLM calls and RAG
- **Real-time Chat**: Supabase Realtime for live message delivery
- **Response Delivery**: Edge Functions send responses back to Trillian

## API Design

### Supabase Auto-generated APIs

```typescript
// Authentication (Supabase Auth)
POST /auth/v1/token
POST /auth/v1/logout
GET /auth/v1/user

// Database APIs (PostgREST)
GET /rest/v1/documents
POST /rest/v1/documents
GET /rest/v1/chat_sessions
POST /rest/v1/chat_sessions
GET /rest/v1/messages
POST /rest/v1/messages

// Storage APIs
GET /storage/v1/bucket/documents/publicUrl/:filePath
POST /storage/v1/object/documents
```

### Custom Backend APIs

```typescript
// Complex document processing
POST /api/documents/process
POST /api/documents/chunk

// LLM integration
POST /api/chat/generate
POST /api/chat/rag-search

// Admin operations
GET /api/admin/users
POST /api/admin/users
PUT /api/admin/users/:id

// System operations
GET /api/health
GET /api/metrics
```

### Real-time Subscriptions

```typescript
// Supabase Realtime channels
const chatChannel = supabase
  .channel("chat_messages")
  .on(
    "postgres_changes",
    {
      event: "INSERT",
      schema: "public",
      table: "messages",
    },
    handleNewMessage
  )
  .subscribe();
```

## Supabase Cloud Infrastructure

### Managed Services

- **Supabase Hosting**: Managed PostgreSQL, Auth, Storage, and Edge Functions
- **Global CDN**: Automatic content delivery for static assets
- **Auto-scaling**: Built-in scaling for database and services
- **High Availability**: Automatic failover and backup

### Integration Services

- **Supabase Database**: Managed PostgreSQL with pgvector
- **Supabase Storage**: Managed file storage with CDN
- **Supabase Auth**: OAuth2 integration with company identity provider
- **Supabase Realtime**: Real-time subscriptions for chat
- **Supabase Edge Functions**: Serverless functions for API logic

### Monitoring & Observability

- **Supabase Dashboard**: Built-in monitoring and analytics
- **Real-time Metrics**: Database performance and usage metrics
- **Error Tracking**: Automatic error logging and alerting
- **Usage Analytics**: API usage and performance insights

### Development & Testing

- **Supabase CLI**: Local development and testing tools
- **Database Branching**: Isolated development environments
- **Migration Tools**: Database schema versioning and deployment
- **Testing Framework**: Built-in testing utilities

## Environment Configuration

### Development Environment

```typescript
// Supabase development configuration
const supabaseConfig = {
  supabase: {
    url: process.env.SUPABASE_URL,
    anonKey: process.env.SUPABASE_ANON_KEY,
    serviceRoleKey: process.env.SUPABASE_SERVICE_ROLE_KEY,
  },
  openai: {
    apiKey: process.env.OPENAI_API_KEY,
  },
  anthropic: {
    apiKey: process.env.ANTHROPIC_API_KEY,
  },
};
```

### Production Deployment

- **Supabase Project**: Managed cloud project with all services
- **Environment Variables**: Centralized configuration management
- **Database Backups**: Automatic daily backups with point-in-time recovery
- **Monitoring**: Built-in performance monitoring and alerting
- **Security**: Row Level Security policies and API key management

## Containerization

### Development Environment

```dockerfile
# Optional: Development Dockerfile for .NET backend
FROM mcr.microsoft.com/dotnet/sdk:8.0
WORKDIR /app

# Install Node.js for any frontend tooling
RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash - \
    && apt-get install -y nodejs

# Install Supabase CLI for local development
RUN npm install -g supabase

# Set up development environment
ENV ASPNETCORE_ENVIRONMENT=Development
ENV DOTNET_USE_POLLING_FILE_WATCHER=true

WORKDIR /workspace
```

### Production Deployment

- **Supabase Edge Functions**: Serverless deployment for API logic
- **Static Frontend**: Deploy React app to Supabase hosting or CDN
- **Container Optional**: .NET backend can be containerized if needed for complex processing
- **Recommended**: Use Supabase managed services for simplified deployment

### Supabase Local Development

- **Local Database**: PostgreSQL with pgvector via Supabase CLI
- **Local Storage**: MinIO for file storage
- **Local Auth**: Supabase Auth service
- **Local Edge Functions**: Deno runtime for serverless functions

### Supabase Edge Functions

```typescript
// Edge Function for document processing
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

serve(async (req) => {
  const supabase = createClient(
    Deno.env.get("SUPABASE_URL") ?? "",
    Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? ""
  );

  // Process document upload and RAG
  // Return response
});
```

### CLI Configuration Management (iChat.Tool)

```csharp
// ConfigService.cs in iChat.Tool project
using System.Text.Json;
using Microsoft.Extensions.Configuration;

public interface IConfigService
{
    string GetApiUrl();
    string? GetApiKey();
    int GetTimeout();
    string GetFormat();
    void SetConfig(string key, string value);
    T GetConfig<T>(string key);
}

public class ConfigService : IConfigService
{
    private readonly string _configPath;
    private CLIConfig _config;

    public ConfigService()
    {
        var homeDirectory = Environment.GetFolderPath(Environment.SpecialFolder.UserProfile);
        _configPath = Path.Combine(homeDirectory, ".ichat", "config.json");
        LoadConfig();
    }

    private void LoadConfig()
    {
        var defaultConfig = new CLIConfig
        {
            ApiUrl = Environment.GetEnvironmentVariable("ICHAT_API_URL") ?? "https://api.ichat.company.com",
            Timeout = 30000,
            Format = "table"
        };

        try
        {
            if (File.Exists(_configPath))
            {
                var json = File.ReadAllText(_configPath);
                var fileConfig = JsonSerializer.Deserialize<CLIConfig>(json);
                _config = MergeConfigs(defaultConfig, fileConfig);
            }
            else
            {
                _config = defaultConfig;
                SaveConfig();
            }
        }
        catch
        {
            _config = defaultConfig;
        }
    }

    public void SetConfig(string key, string value)
    {
        var property = typeof(CLIConfig).GetProperty(key);
        if (property != null)
        {
            property.SetValue(_config, value);
            SaveConfig();
        }
    }

    public T GetConfig<T>(string key)
    {
        var property = typeof(CLIConfig).GetProperty(key);
        return property != null ? (T)property.GetValue(_config)! : default!;
    }

    private void SaveConfig()
    {
        var configDir = Path.GetDirectoryName(_configPath)!;
        Directory.CreateDirectory(configDir);

        var json = JsonSerializer.Serialize(_config, new JsonSerializerOptions { WriteIndented = true });
        File.WriteAllText(_configPath, json);
    }
}

public class CLIConfig
{
    public string ApiUrl { get; set; } = string.Empty;
    public string? ApiKey { get; set; }
    public int Timeout { get; set; }
    public string Format { get; set; } = string.Empty;
}
```

### Docker Compose Development

```yaml
version: "3.8"
services:
  api:
    build:
      context: ./iChat.Api
      dockerfile: Dockerfile.dev
    ports:
      - "5000:5000"
      - "5001:5001"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=https://+:5001;http://+:5000
      - ConnectionStrings__DefaultConnection=Host=db;Port=5432;Database=ichat;Username=postgres;Password=password
    volumes:
      - ./iChat.Api:/app
      - ./iChat.Shared:/shared
    depends_on:
      - db
      - redis

  service:
    build:
      context: ./iChat.Service
      dockerfile: Dockerfile.dev
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - API_BASE_URL=http://api:5000
    volumes:
      - ./iChat.Service:/app
      - ./iChat.Shared:/shared
    depends_on:
      - api

  frontend:
    build:
      context: ./iChat.Frontend
      dockerfile: Dockerfile.dev
    ports:
      - "5173:5173"
    volumes:
      - ./iChat.Frontend:/app
      - /app/node_modules
    depends_on:
      - api

  db:
    image: pgvector/pgvector:pg15
    environment:
      - POSTGRES_DB=ichat
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

volumes:
  postgres_data:
```

## Development Environment

### DevContainer Configuration

```json
// .devcontainer/devcontainer.json
{
  "name": "iChat Development with Supabase",
  "image": "mcr.microsoft.com/devcontainers/dotnet:8.0",
  "features": {
    "ghcr.io/devcontainers/features/node:1": {
      "version": "18"
    },
    "ghcr.io/devcontainers/features/github-cli:1": {}
  },
  "customizations": {
    "vscode": {
      "extensions": [
        "ms-dotnettools.csharp",
        "ms-dotnettools.csdevkit",
        "bradlc.vscode-tailwindcss",
        "ms-vscode.vscode-json",
        "esbenp.prettier-vscode",
        "supabase.supabase"
      ],
      "settings": {
        "dotnet.completion.showCompletionItemsFromUnimportedNamespaces": true,
        "editor.formatOnSave": true,
        "editor.defaultFormatter": "ms-dotnettools.csharp",
        "[csharp]": {
          "editor.defaultFormatter": "ms-dotnettools.csharp"
        },
        "[typescript]": {
          "editor.defaultFormatter": "esbenp.prettier-vscode"
        },
        "[javascript]": {
          "editor.defaultFormatter": "esbenp.prettier-vscode"
        }
      }
    }
  },
  "forwardPorts": [5000, 5001, 5173],
  "postCreateCommand": "dotnet restore && cd Frontend && npm install",
  "remoteUser": "vscode",
  "mounts": [
    "source=${localEnv:HOME}/.supabase,target=/home/<USER>/.supabase,type=volume"
  ]
}
```

### Local Development Setup

```bash
# Install Supabase CLI
npm install -g supabase

# Start local Supabase development environment
supabase start

# Set up environment variables
cp .env.example .env.local

# Build shared project
dotnet build iChat.Shared

# Run REST API
dotnet run --project iChat.Api

# Run chat bot service
dotnet run --project iChat.Service

# Run CLI (for admin tasks)
dotnet run --project iChat.Tool

# Run frontend development server
cd iChat.Frontend && npm run dev
```

### Supabase Local Development

- **Local Database**: PostgreSQL with pgvector
- **Local Storage**: MinIO for file storage
- **Local Auth**: Supabase Auth service
- **Local Edge Functions**: Deno runtime for serverless functions
