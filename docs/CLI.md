Not planned currently

# Command Line Interface

## CLI Framework
- **Language**: C# (.NET 10+)
- **Runtime**: .NET native executable or containerized deployment
- **Distribution**: Self-contained executable (Docker optional)
- **Alternative**: Supabase CLI for database and deployment operations

```xml
<PackageReference Include="System.CommandLine" Version="2.0.0-beta4.22272.1" />
<PackageReference Include="Microsoft.Extensions.Hosting" Version="10.0.0" />
<PackageReference Include="Microsoft.Extensions.Configuration" Version="10.0.0" />
<PackageReference Include="Microsoft.Extensions.Http" Version="10.0.0" />
<PackageReference Include="Spectre.Console" Version="0.47.0" />
<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
```

## CLI Container Usage
```bash
# Pull and run CLI container
docker pull ichat/cli:latest

# Run CLI commands via container
docker run --rm -it \
  -v $(pwd):/workspace \
  -v ~/.ichat:/root/.ichat \
  ichat/cli:latest auth login

# Create alias for easier usage
alias ichat-cli='docker run --rm -it -v $(pwd):/workspace -v ~/.ichat:/root/.ichat ichat/cli:latest'

# Alternative: Download native executable
# Windows: ichat-cli.exe
# macOS: ichat-cli
# Linux: ichat-cli

# Use CLI normally
ichat-cli docs upload document.pdf
ichat-cli chat start
```

## CLI Commands Structure
```bash
ichat-cli auth login                    # Authenticate with API key
ichat-cli auth logout                   # Clear authentication
ichat-cli docs upload <file>            # Upload document
ichat-cli docs list                     # List all documents
ichat-cli docs delete <id>              # Delete document
ichat-cli users create <email>          # Create user
ichat-cli users list                    # List users
ichat-cli chat start                    # Start interactive chat
ichat-cli queries list                  # List escalated queries
ichat-cli queries respond <id>          # Respond to escalated query
ichat-cli system status                 # System health check
ichat-cli config set <key> <value>      # Set configuration
ichat-cli config get <key>              # Get configuration
```

## CLI Application Structure
```csharp
// Program.cs - Main entry point
using System.CommandLine;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using IChatCli.Commands;
using IChatCli.Services;

var builder = Host.CreateApplicationBuilder(args);

// Add services
builder.Services.AddHttpClient();
builder.Services.AddSingleton<ConfigService>();
builder.Services.AddSingleton<ApiClient>();

var host = builder.Build();

var rootCommand = new RootCommand("iChat AI Assistant CLI Tool");

// Add command modules
rootCommand.AddCommand(AuthCommand.CreateCommand(host.Services));
rootCommand.AddCommand(DocsCommand.CreateCommand(host.Services));
rootCommand.AddCommand(UsersCommand.CreateCommand(host.Services));
rootCommand.AddCommand(ChatCommand.CreateCommand(host.Services));
rootCommand.AddCommand(QueriesCommand.CreateCommand(host.Services));
rootCommand.AddCommand(SystemCommand.CreateCommand(host.Services));
rootCommand.AddCommand(ConfigCommand.CreateCommand(host.Services));

return await rootCommand.InvokeAsync(args);
```
